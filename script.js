// Global Variables
let currentSection = 0;
let currentImageIndex = 0;
let isTyping = false;
let musicPlaying = false;

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeStars();
    startTypewriter();
    initializeMusic();
    startCountdown();
    
    // Hide all sections except opening
    const sections = document.querySelectorAll('.section');
    sections.forEach((section, index) => {
        if (index !== 0) {
            section.style.display = 'none';
        }
    });
});

// Stars Animation
function initializeStars() {
    const canvas = document.getElementById('starsCanvas');
    const ctx = canvas.getContext('2d');
    
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
    
    const stars = [];
    const shootingStars = [];
    
    // Create regular stars
    for (let i = 0; i < 200; i++) {
        stars.push({
            x: Math.random() * canvas.width,
            y: Math.random() * canvas.height,
            size: Math.random() * 2,
            opacity: Math.random(),
            twinkleSpeed: Math.random() * 0.02 + 0.01
        });
    }
    
    // Create shooting stars
    function createShootingStar() {
        shootingStars.push({
            x: Math.random() * canvas.width,
            y: Math.random() * canvas.height * 0.5,
            length: Math.random() * 80 + 20,
            speed: Math.random() * 10 + 5,
            opacity: 1,
            angle: Math.random() * Math.PI / 4 + Math.PI / 4
        });
    }
    
    // Create shooting star every 3-7 seconds
    setInterval(createShootingStar, Math.random() * 4000 + 3000);
    
    function animate() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Draw regular stars
        stars.forEach(star => {
            star.opacity += star.twinkleSpeed;
            if (star.opacity > 1 || star.opacity < 0) {
                star.twinkleSpeed = -star.twinkleSpeed;
            }
            
            ctx.beginPath();
            ctx.arc(star.x, star.y, star.size, 0, Math.PI * 2);
            ctx.fillStyle = `rgba(255, 255, 255, ${Math.abs(star.opacity)})`;
            ctx.fill();
        });
        
        // Draw shooting stars
        shootingStars.forEach((star, index) => {
            const gradient = ctx.createLinearGradient(
                star.x, star.y,
                star.x - Math.cos(star.angle) * star.length,
                star.y - Math.sin(star.angle) * star.length
            );
            gradient.addColorStop(0, `rgba(255, 255, 255, ${star.opacity})`);
            gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
            
            ctx.beginPath();
            ctx.moveTo(star.x, star.y);
            ctx.lineTo(
                star.x - Math.cos(star.angle) * star.length,
                star.y - Math.sin(star.angle) * star.length
            );
            ctx.strokeStyle = gradient;
            ctx.lineWidth = 2;
            ctx.stroke();
            
            star.x += Math.cos(star.angle) * star.speed;
            star.y += Math.sin(star.angle) * star.speed;
            star.opacity -= 0.01;
            
            if (star.opacity <= 0 || star.x > canvas.width || star.y > canvas.height) {
                shootingStars.splice(index, 1);
            }
        });
        
        requestAnimationFrame(animate);
    }
    
    animate();
    
    // Resize canvas on window resize
    window.addEventListener('resize', () => {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
    });
}

// Typewriter Effect
function startTypewriter() {
    const text = "Selamat Ulang Tahun, Mamah";
    const typewriterElement = document.getElementById('typewriter');
    let index = 0;
    isTyping = true;

    function type() {
        if (index < text.length) {
            typewriterElement.textContent += text.charAt(index);
            index++;
            setTimeout(type, 120);
        } else {
            isTyping = false;
            // Hide cursor after typing is complete
            setTimeout(() => {
                const cursor = document.querySelector('.cursor');
                if (cursor) cursor.style.display = 'none';
            }, 2000);
        }
    }

    // Start typing after a short delay
    setTimeout(type, 1000);
}

// Music Control
function initializeMusic() {
    const music = document.getElementById('bgMusic');
    const musicToggle = document.getElementById('musicToggle');
    const musicIcon = document.querySelector('.music-icon');
    
    musicToggle.addEventListener('click', () => {
        if (musicPlaying) {
            music.pause();
            musicIcon.textContent = '🔇';
            musicPlaying = false;
        } else {
            music.play().catch(e => console.log('Audio play failed:', e));
            musicIcon.textContent = '🎵';
            musicPlaying = true;
        }
    });
    
    // Auto-play music (might be blocked by browser)
    setTimeout(() => {
        music.play().then(() => {
            musicPlaying = true;
            musicIcon.textContent = '🎵';
        }).catch(e => {
            console.log('Auto-play blocked:', e);
            musicIcon.textContent = '🔇';
        });
    }, 2000);
}

// Section Navigation
function nextSection() {
    const sections = document.querySelectorAll('.section');
    const currentSectionElement = sections[currentSection];
    
    currentSection++;
    
    if (currentSection < sections.length) {
        const nextSectionElement = sections[currentSection];
        
        // Hide current section
        currentSectionElement.classList.remove('active');
        setTimeout(() => {
            currentSectionElement.style.display = 'none';
            
            // Show next section
            nextSectionElement.style.display = 'flex';
            setTimeout(() => {
                nextSectionElement.classList.add('active');
            }, 50);
        }, 500);
    }
}

// Gallery Functions
function nextImage() {
    const items = document.querySelectorAll('.gallery-item');
    const dots = document.querySelectorAll('.dot');
    
    items[currentImageIndex].classList.remove('active');
    dots[currentImageIndex].classList.remove('active');
    
    currentImageIndex = (currentImageIndex + 1) % items.length;
    
    items[currentImageIndex].classList.add('active');
    dots[currentImageIndex].classList.add('active');
}

function prevImage() {
    const items = document.querySelectorAll('.gallery-item');
    const dots = document.querySelectorAll('.dot');
    
    items[currentImageIndex].classList.remove('active');
    dots[currentImageIndex].classList.remove('active');
    
    currentImageIndex = (currentImageIndex - 1 + items.length) % items.length;
    
    items[currentImageIndex].classList.add('active');
    dots[currentImageIndex].classList.add('active');
}

function currentImage(index) {
    const items = document.querySelectorAll('.gallery-item');
    const dots = document.querySelectorAll('.dot');
    
    items[currentImageIndex].classList.remove('active');
    dots[currentImageIndex].classList.remove('active');
    
    currentImageIndex = index - 1;
    
    items[currentImageIndex].classList.add('active');
    dots[currentImageIndex].classList.add('active');
}

// Auto-advance gallery every 5 seconds
setInterval(() => {
    if (currentSection === 1) { // Only when on gallery section
        nextImage();
    }
}, 5000);

// Letter Functions
function openLetter() {
    const envelope = document.getElementById('envelope');
    const letterPaper = document.getElementById('letterPaper');
    const clickHint = document.querySelector('.click-hint');

    envelope.classList.add('opened');
    if (clickHint) clickHint.style.display = 'none';

    // Show letter paper with delay
    setTimeout(() => {
        if (letterPaper) {
            letterPaper.style.display = 'block';
        }
    }, 400);

    // Play letter opening sound effect (if available)
    playSound('letterOpen');
}

// Countdown Functions
function startCountdown() {
    function updateCountdown() {
        const now = new Date().getTime();
        const currentYear = new Date().getFullYear();
        let targetDate = new Date(currentYear, 6, 24, 0, 0, 0).getTime(); // July 24th (month is 0-indexed)
        
        // If birthday has passed this year, target next year
        if (now > targetDate) {
            targetDate = new Date(currentYear + 1, 6, 24, 0, 0, 0).getTime();
        }
        
        const distance = targetDate - now;
        
        if (distance > 0) {
            const days = Math.floor(distance / (1000 * 60 * 60 * 24));
            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((distance % (1000 * 60)) / 1000);
            
            document.getElementById('days').textContent = days.toString().padStart(3, '0');
            document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
            document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
            document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');
        } else {
            // It's her birthday!
            document.getElementById('days').textContent = '000';
            document.getElementById('hours').textContent = '00';
            document.getElementById('minutes').textContent = '00';
            document.getElementById('seconds').textContent = '00';
            document.getElementById('countdownText').textContent = '🎉 Hari ini adalah hari istimewa Mamah! 🎉';
        }
    }
    
    updateCountdown();
    setInterval(updateCountdown, 1000);
}

function checkBirthday() {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const birthday = new Date(now.getFullYear(), 6, 24); // July 24th
    
    if (today.getTime() === birthday.getTime()) {
        showSurpriseModal();
        createConfetti();
    } else {
        // Show modal anyway for demo purposes
        showSurpriseModal();
        createConfetti();
    }
}

// Modal Functions
function showSurpriseModal() {
    const modal = document.getElementById('surpriseModal');
    modal.classList.add('show');
    
    // Play celebration sound
    playSound('celebration');
}

function closeModal() {
    const modal = document.getElementById('surpriseModal');
    modal.classList.remove('show');
    
    // Stop confetti
    document.getElementById('confetti-container').innerHTML = '';
}

// Confetti Animation
function createConfetti() {
    const container = document.getElementById('confetti-container');
    const colors = ['#e91e63', '#ff6b9d', '#ffd700', '#ff9800', '#4caf50', '#2196f3'];
    
    function createConfettiPiece() {
        const confetti = document.createElement('div');
        confetti.className = 'confetti';
        confetti.style.left = Math.random() * 100 + '%';
        confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
        confetti.style.animationDelay = Math.random() * 2 + 's';
        confetti.style.animationDuration = (Math.random() * 3 + 2) + 's';
        
        container.appendChild(confetti);
        
        // Remove confetti after animation
        setTimeout(() => {
            if (confetti.parentNode) {
                confetti.parentNode.removeChild(confetti);
            }
        }, 5000);
    }
    
    // Create confetti burst
    for (let i = 0; i < 100; i++) {
        setTimeout(createConfettiPiece, i * 50);
    }
    
    // Continue creating confetti for 10 seconds
    const confettiInterval = setInterval(createConfettiPiece, 100);
    setTimeout(() => {
        clearInterval(confettiInterval);
    }, 10000);
}

// Sound Effects (placeholder function)
function playSound(soundType) {
    // This function can be expanded to play actual sound effects
    // For now, it's a placeholder
    console.log(`Playing sound: ${soundType}`);
    
    // Example implementation:
    // const audio = new Audio(`assets/sounds/${soundType}.mp3`);
    // audio.play().catch(e => console.log('Sound play failed:', e));
}

// Keyboard Navigation
document.addEventListener('keydown', (e) => {
    if (e.key === 'ArrowRight' || e.key === ' ') {
        if (currentSection === 1) {
            nextImage();
        } else {
            nextSection();
        }
    } else if (e.key === 'ArrowLeft') {
        if (currentSection === 1) {
            prevImage();
        }
    } else if (e.key === 'Enter') {
        if (currentSection === 2) {
            openLetter();
        } else if (currentSection === 3) {
            checkBirthday();
        }
    }
});

// Touch/Swipe Support for Mobile
let touchStartX = 0;
let touchEndX = 0;

document.addEventListener('touchstart', (e) => {
    touchStartX = e.changedTouches[0].screenX;
});

document.addEventListener('touchend', (e) => {
    touchEndX = e.changedTouches[0].screenX;
    handleSwipe();
});

function handleSwipe() {
    const swipeThreshold = 50;
    const diff = touchStartX - touchEndX;
    
    if (Math.abs(diff) > swipeThreshold) {
        if (diff > 0) {
            // Swipe left - next
            if (currentSection === 1) {
                nextImage();
            } else {
                nextSection();
            }
        } else {
            // Swipe right - previous
            if (currentSection === 1) {
                prevImage();
            }
        }
    }
}

// Smooth scrolling for better UX
document.documentElement.style.scrollBehavior = 'smooth';

// Prevent right-click context menu for a more immersive experience
document.addEventListener('contextmenu', (e) => {
    e.preventDefault();
});

// Add some Easter eggs
let clickCount = 0;
document.addEventListener('click', () => {
    clickCount++;
    if (clickCount === 50) {
        alert('💖 Kamu sangat perhatian! Wiwin pasti senang! 💖');
    }
});

// Performance optimization: Pause animations when not visible
document.addEventListener('visibilitychange', () => {
    const music = document.getElementById('bgMusic');
    if (document.hidden) {
        if (musicPlaying) {
            music.pause();
        }
    } else {
        if (musicPlaying) {
            music.play().catch(e => console.log('Resume play failed:', e));
        }
    }
});
