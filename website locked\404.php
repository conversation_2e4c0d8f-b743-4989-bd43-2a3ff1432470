<?php
// Set timezone ke Asia/Jakarta
date_default_timezone_set('Asia/Jakarta');

// Target deadline: 30 Juli 2025 jam 23:59:59 WIB
$target_date = '2025-07-30 23:59:59';
$target_timestamp = strtotime($target_date);

// Tanggal penghapusan data: 3 hari setelah deadline
$deletion_date = '2025-08-02 23:59:59';
$deletion_timestamp = strtotime($deletion_date);

// Waktu server saat ini
$current_timestamp = time();

// Jika waktu pembayaran belum habis dan diakses langsung, redirect ke index.php
if ($current_timestamp < $target_timestamp) {
    header('Location: index.php');
    exit;
}

// Jika waktu penghapusan data sudah habis, tampilkan pesan data telah dihapus
$data_deleted = $current_timestamp >= $deletion_timestamp;

// Hitung sisa waktu penghapusan data
$deletion_remaining = $deletion_timestamp - $current_timestamp;
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $data_deleted ? 'Data Telah Dihapus' : 'Waktu Pembayaran Habis'; ?></title>
    <link rel="shortcut icon" href="https://client.indowebsolution.com/uploads/company/favicon.png" />
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .container {
            text-align: center;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            max-width: 600px;
            width: 90%;
        }
        
        .error-code {
            font-size: 6rem;
            font-weight: bold;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .error-message {
            font-size: 1.8rem;
            margin-bottom: 1rem;
            font-weight: 300;
        }
        
        .error-description {
            font-size: 1rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .countdown-section {
            background: rgba(255, 255, 255, 0.15);
            padding: 1.5rem;
            border-radius: 15px;
            margin: 2rem 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .countdown-label {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #ffeb3b;
        }
        
        .countdown {
            font-size: 1.5rem;
            font-weight: bold;
            color: #ff5722;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }
        
        .countdown.expired {
            color: #f44336;
            font-size: 1.3rem;
        }
        
        .target-info {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 0.5rem;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 30px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            font-weight: 500;
            margin: 0 10px;
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn.whatsapp {
            background: rgba(37, 211, 102, 0.3);
            border-color: rgba(37, 211, 102, 0.5);
        }
        
        .btn.whatsapp:hover {
            background: rgba(37, 211, 102, 0.5);
        }
        
        .icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        
        .footer {
            margin-top: 2rem;
            font-size: 0.9rem;
            opacity: 0.7;
        }
        
        .warning-box {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid rgba(255, 193, 7, 0.4);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .danger-box {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.4);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon"><?php echo $data_deleted ? '💀' : '⏰'; ?></div>
        <div class="error-code"><?php echo $data_deleted ? '410' : '403'; ?></div>
        <div class="error-message">
            <?php echo $data_deleted ? 'Data Telah Dihapus' : 'Waktu Pembayaran Habis'; ?>
        </div>
        
        <?php if ($data_deleted): ?>
            <div class="danger-box">
                <div class="error-description">
                    <strong>⚠️ SEMUA DATA TELAH DIHAPUS PERMANEN</strong><br><br>
                    Data website telah dihapus pada tanggal <strong>2 Agustus 2025 jam 23:59 WIB</strong> karena tidak ada pembayaran yang diterima dalam batas waktu yang ditentukan.
                    <br><br>
                    Untuk pemulihan data atau layanan baru, silakan hubungi administrator.
                </div>
            </div>
        <?php else: ?>
            <div class="warning-box">
                <div class="error-description">
                    Waktu pembayaran telah berakhir pada <strong>30 Juli 2025 jam 23:59 WIB</strong>.
                    <br><br>
                    Website masih dapat dipulihkan jika pembayaran dilakukan sebelum penghapusan data.
                </div>
            </div>
            
            <div class="countdown-section">
                <div class="countdown-label">⚠️ Data akan dihapus permanen dalam:</div>
                <div id="deletion-countdown" class="countdown">Memuat...</div>
                <div class="target-info">2 Agustus 2025, 23:59 WIB</div>
            </div>
        <?php endif; ?>
        
        <div style="margin: 2rem 0;">
            <a href="https://wa.me/6282211219993" class="btn whatsapp" target="_blank" rel="noopener">
                📱 WhatsApp: 0822-1121-9993
            </a>
            <a href="https://wa.me/6281911919993" class="btn whatsapp" target="_blank" rel="noopener">
                📱 WhatsApp: 0819-1191-9993
            </a>
        </div>
        
        <div class="footer">
            © 2025 Indo Web Solution<br>
            <small>Halaman ini hanya dapat diakses setelah batas waktu pembayaran</small>
        </div>
    </div>
    
    <?php if (!$data_deleted): ?>
    <script>
        // Data dari server PHP
        const serverData = {
            deletionTimestamp: <?php echo $deletion_timestamp * 1000; ?>, // Convert ke milliseconds
            serverTime: <?php echo $current_timestamp * 1000; ?>
        };

        // Hitung offset antara server time dan client time
        const clientTime = new Date().getTime();
        const timeOffset = serverData.serverTime - clientTime;

        function startDeletionCountdown() {
            const el = document.getElementById('deletion-countdown');

            function update() {
                // Gunakan waktu client + offset untuk sinkronisasi dengan server
                const now = new Date().getTime() + timeOffset;
                const distance = serverData.deletionTimestamp - now;

                if (distance < 0) {
                    el.innerHTML = "Data telah dihapus!";
                    el.classList.add('expired');
                    // Auto refresh halaman setelah data dihapus
                    setTimeout(() => {
                        window.location.reload();
                    }, 3000);
                    return;
                }

                const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((distance % (1000 * 60)) / 1000);

                el.innerHTML = `${days} hari ${hours} jam ${minutes} menit ${seconds} detik`;
            }

            update();
            setInterval(update, 1000); // Update setiap detik
        }

        // Mulai countdown penghapusan data
        startDeletionCountdown();

        // Auto refresh setiap 10 menit untuk memastikan status terbaru
        setInterval(() => {
            window.location.reload();
        }, 600000); // 10 menit
    </script>
    <?php endif; ?>
</body>
</html>
