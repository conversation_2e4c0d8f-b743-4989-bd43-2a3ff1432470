<?php
// Set timezone ke Asia/Jakarta
date_default_timezone_set('Asia/Jakarta');

// Target deadline: 30 Juli 2025 jam 23:59:59 WIB
$target_date = '2025-07-30 23:59:59';
$target_timestamp = strtotime($target_date);

// Tanggal penghapusan data: 3 hari setelah deadline
$deletion_date = '2025-08-02 23:59:59';
$deletion_timestamp = strtotime($deletion_date);

// Waktu server saat ini
$current_timestamp = time();

// Jika waktu sudah habis, redirect ke 404
if ($current_timestamp >= $target_timestamp) {
    header('Location: 404.php');
    exit;
}

// Hitung selisih waktu
$time_remaining = $target_timestamp - $current_timestamp;
$deletion_remaining = $deletion_timestamp - $current_timestamp;
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>🔒 Akses Website Dibatasi 🔒</title>
    <link rel="shortcut icon" href="https://client.indowebsolution.com/uploads/company/favicon.png" />
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #6b7280;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #1f2937;
        }

        .container {
            background-color: #f3f4f6;
            padding: 1.5rem;
            border-radius: 0.75rem;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            max-width: 24rem;
            width: 100%;
            text-align: center;
        }

        .logo {
            margin: 0 auto 1rem auto;
            width: 5rem;
            height: 5rem;
            object-fit: contain;
        }

        .title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #dc2626;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .subtitle {
            margin-bottom: 0.5rem;
        }

        .description {
            margin-bottom: 1rem;
        }

        .info-box {
            background-color: #dbeafe;
            padding: 0.75rem;
            border-radius: 0.5rem;
            box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
            margin-bottom: 1rem;
        }

        .info-box.blue {
            background-color: #dbeafe;
        }

        .info-box.white {
            background-color: white;
        }

        .info-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .info-label.blue {
            color: #1d4ed8;
        }

        .countdown {
            font-size: 1.125rem;
            font-weight: bold;
        }

        .countdown.orange {
            color: #ea580c;
        }

        .countdown.red {
            color: #dc2626;
        }

        .server-time {
            font-size: 0.875rem;
            font-family: 'Courier New', monospace;
            color: #1e40af;
        }

        .target-info {
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 0.25rem;
        }

        .contact-section {
            text-align: left;
            font-weight: 600;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            justify-content: center;
        }

        .contact-icon {
            width: 1.25rem;
            height: 1.25rem;
            color: #16a34a;
        }

        .contact-link {
            color: #15803d;
            text-decoration: none;
        }

        .contact-link:hover {
            text-decoration: underline;
        }
    </style>
</head>

<body>
    <div class="container">
        <img src="https://client.indowebsolution.com/uploads/company/favicon.png" alt="Logo Indo Web Solution" class="logo" />
        <h1 class="title">
            🔒<br>Akses Website Dibatasi
        </h1>
        <p class="subtitle">Semua database telah terenkripsi.</p>

        <!-- Countdown Timer -->
        <div class="info-box white">
            <p class="info-label">⏳ Sisa Waktu Pembayaran:</p>
            <div id="countdown1" class="countdown orange">Memuat...</div>
            <div class="target-info">30 Juli 2025, 23:59 WIB</div>
        </div>

        <!-- Data Deletion Countdown -->
        <div class="info-box white">
            <p class="info-label">⚠️ Semua data akan dihapus dalam:</p>
            <div id="countdown2" class="countdown red">Memuat...</div>
            <div class="target-info">2 Agustus 2025, 23:59 WIB</div>
        </div>

        <div class="contact-section">
        <p class="description"><center>Lanjutkan proses aktivasi, hubungi:</center></p>
            <div class="contact-item">
                <svg xmlns="http://www.w3.org/2000/svg" class="contact-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M.057 24l1.687-6.163c-1.041-1.804-1.588-3.849-1.587-5.946.003-6.556 5.338-11.891 11.893-11.891 3.181.001 6.167 1.24 8.413 3.488 2.245 2.248 3.481 5.236 3.479 8.414-.003 6.557-5.338 11.892-11.893 11.892-1.99-.001-3.951-.5-5.688-1.448l-6.305 1.654zm6.597-3.807c1.676.995 3.276 1.591 5.392 1.592 5.448 0 9.886-4.434 9.889-9.885.002-5.462-4.415-9.89-9.881-9.892-5.452 0-9.887 4.434-9.889 9.884-.001 2.225.651 3.891 1.746 5.634l-.999 3.648 3.742-.981zm11.387-5.464c-.074-.124-.272-.198-.57-.347-.297-.149-1.758-.868-2.031-.967-.272-.099-.47-.149-.669.149-.198.297-.768.967-.941 1.165-.173.198-.347.223-.644.074-.297-.149-1.255-.462-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.297-.347.446-.521.151-.172.2-.296.3-.495.099-.198.05-.372-.025-.521-.075-.148-.669-1.611-.916-2.206-.242-.579-.487-.501-.669-.51l-.57-.01c-.198 0-.52.074-.792.372s-1.04 1.016-1.04 2.479 1.065 2.876 1.213 3.074c.149.198 2.095 3.2 5.076 4.487.709.306 1.263.489 1.694.626.712.226 1.36.194 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.695.248-1.29.173-1.414z"/>
                </svg>
                <a href="https://wa.me/6282211219993" target="_blank" rel="noopener" class="contact-link">0822-1121-9993</a>
            </div>
            <div class="contact-item">
                <svg xmlns="http://www.w3.org/2000/svg" class="contact-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M.057 24l1.687-6.163c-1.041-1.804-1.588-3.849-1.587-5.946.003-6.556 5.338-11.891 11.893-11.891 3.181.001 6.167 1.24 8.413 3.488 2.245 2.248 3.481 5.236 3.479 8.414-.003 6.557-5.338 11.892-11.893 11.892-1.99-.001-3.951-.5-5.688-1.448l-6.305 1.654zm6.597-3.807c1.676.995 3.276 1.591 5.392 1.592 5.448 0 9.886-4.434 9.889-9.885.002-5.462-4.415-9.89-9.881-9.892-5.452 0-9.887 4.434-9.889 9.884-.001 2.225.651 3.891 1.746 5.634l-.999 3.648 3.742-.981zm11.387-5.464c-.074-.124-.272-.198-.57-.347-.297-.149-1.758-.868-2.031-.967-.272-.099-.47-.149-.669.149-.198.297-.768.967-.941 1.165-.173.198-.347.223-.644.074-.297-.149-1.255-.462-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.297-.347.446-.521.151-.172.2-.296.3-.495.099-.198.05-.372-.025-.521-.075-.148-.669-1.611-.916-2.206-.242-.579-.487-.501-.669-.51l-.57-.01c-.198 0-.52.074-.792.372s-1.04 1.016-1.04 2.479 1.065 2.876 1.213 3.074c.149.198 2.095 3.2 5.076 4.487.709.306 1.263.489 1.694.626.712.226 1.36.194 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.695.248-1.29.173-1.414z"/>
                </svg>
                <a href="https://wa.me/6281911919993" target="_blank" rel="noopener" class="contact-link">0819-1191-9993</a>
            </div>
        </div>
    </div>

    <script>
        // Data dari server PHP
        const serverData = {
            targetTimestamp: <?php echo $target_timestamp * 1000; ?>, // Convert ke milliseconds
            deletionTimestamp: <?php echo $deletion_timestamp * 1000; ?>,
            serverTime: <?php echo $current_timestamp * 1000; ?>
        };

        // Hitung offset antara server time dan client time
        const clientTime = new Date().getTime();
        const timeOffset = serverData.serverTime - clientTime;

        function startCountdown(id, targetTimestamp) {
            const el = document.getElementById(id);

            function update() {
                // Gunakan waktu client + offset untuk sinkronisasi dengan server
                const now = new Date().getTime() + timeOffset;
                const distance = targetTimestamp - now;

                if (distance < 0) {
                    el.innerHTML = "Waktu habis!";
                    // Auto redirect ke 404 setelah countdown habis
                    setTimeout(() => {
                        window.location.href = '404.php';
                    }, 2000);
                    return;
                }

                const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((distance % (1000 * 60)) / 1000);

                el.innerHTML = `${days} hari ${hours} jam ${minutes} menit ${seconds} detik`;
            }

            update();
            setInterval(update, 1000); // Update setiap detik
        }

        // Mulai countdown
        startCountdown("countdown1", serverData.targetTimestamp);
        startCountdown("countdown2", serverData.deletionTimestamp);

        // Refresh halaman setiap 5 menit untuk sinkronisasi server time
        setInterval(() => {
            fetch('get_server_time.php')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'expired') {
                        window.location.href = '404.php';
                    }
                })
                .catch(error => console.log('Server check failed:', error));
        }, 300000); // 5 menit
    </script>
</body>
</html>
