/* Reset dan Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #ffeef8 0%, #ffe4e6 50%, #fdf2f8 100%);
    overflow-x: hidden;
    color: #4a4a4a;
}

/* Music Control */
.music-control {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.music-btn {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.music-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.music-icon {
    font-size: 20px;
    animation: musicPulse 2s infinite;
}

@keyframes musicPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Section Styles */
.section {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    position: relative;
    opacity: 0;
    transform: translateY(50px);
    transition: all 1s ease;
}

.section.active {
    opacity: 1;
    transform: translateY(0);
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    font-family: 'Dancing Script', cursive;
    font-size: 3.5rem;
    color: #e91e63;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.section-subtitle {
    font-size: 1.2rem;
    color: #666;
    font-weight: 300;
}

/* Opening Section */
#opening {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

#starsCanvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.opening-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.typewriter-container {
    position: relative;
    margin-bottom: 2rem;
}

.typewriter-text {
    font-family: 'Great Vibes', cursive;
    font-size: 4rem;
    color: #ffd700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    min-height: 1.2em;
}

.cursor {
    display: inline-block;
    width: 3px;
    height: 4rem;
    background: #ffd700;
    animation: blink 1s infinite;
    margin-left: 5px;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.subtitle {
    opacity: 0;
    animation: fadeInUp 1s ease forwards;
    animation-delay: 3s;
}

.fade-in-delayed {
    animation-delay: 4s;
}

.subtitle p {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    color: #e8e8e8;
}

.birth-date {
    font-family: 'Dancing Script', cursive;
    font-size: 1.8rem !important;
    color: #ffd700 !important;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Continue Button */
.continue-btn {
    background: linear-gradient(45deg, #ff6b9d, #e91e63);
    border: none;
    padding: 15px 30px;
    border-radius: 50px;
    color: white;
    font-size: 1.1rem;
    font-weight: 500;
    cursor: pointer;
    margin-top: 3rem;
    box-shadow: 0 8px 25px rgba(233, 30, 99, 0.3);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    opacity: 0;
    animation: fadeInUp 1s ease forwards;
    animation-delay: 5s;
}

.continue-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(233, 30, 99, 0.4);
}

.heart-pulse {
    animation: heartPulse 1.5s infinite;
}

@keyframes heartPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

/* Gallery Section */
.gallery-container {
    max-width: 800px;
    width: 100%;
    position: relative;
}

.gallery-slider {
    position: relative;
    height: 500px;
    overflow: hidden;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.gallery-item {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.8s ease;
    background: white;
    display: flex;
    flex-direction: column;
}

.gallery-item.active {
    opacity: 1;
    transform: translateX(0);
}

.image-placeholder {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #ffeef8, #ffe4e6);
    color: #e91e63;
}

.placeholder-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.caption {
    padding: 2rem;
    text-align: center;
    background: white;
}

.caption h3 {
    font-family: 'Dancing Script', cursive;
    font-size: 2rem;
    color: #e91e63;
    margin-bottom: 1rem;
}

.caption p {
    font-size: 1.1rem;
    color: #666;
    line-height: 1.6;
}

/* Gallery Controls */
.gallery-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
    padding: 0 2rem;
}

.gallery-btn {
    background: rgba(233, 30, 99, 0.1);
    border: 2px solid #e91e63;
    color: #e91e63;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.gallery-btn:hover {
    background: #e91e63;
    color: white;
    transform: scale(1.1);
}

.gallery-dots {
    display: flex;
    gap: 10px;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(233, 30, 99, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active {
    background: #e91e63;
    transform: scale(1.3);
}

/* Letter Section */
.letter-container {
    perspective: 1000px;
    margin-bottom: 3rem;
}

.envelope {
    position: relative;
    width: 400px;
    height: 280px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.envelope:hover {
    transform: scale(1.05);
}

.envelope-body {
    width: 100%;
    height: 200px;
    background: linear-gradient(135deg, #fff8e1, #fffde7);
    border: 2px solid #e91e63;
    border-radius: 10px;
    position: relative;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.envelope-flap {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    height: 120px;
    background: linear-gradient(135deg, #ffcdd2, #f8bbd9);
    border: 2px solid #e91e63;
    border-radius: 10px 10px 0 0;
    transform-origin: bottom;
    transition: transform 0.8s ease;
    z-index: 3;
}

.envelope.opened .envelope-flap {
    transform: rotateX(-180deg);
}

.letter-paper {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.05);
    transform: translateY(100px);
    opacity: 0;
    transition: all 0.8s ease;
    overflow-y: auto;
    z-index: 2;
}

.envelope.opened .letter-paper {
    transform: translateY(0);
    opacity: 1;
}

.letter-content {
    font-size: 0.9rem;
    line-height: 1.6;
    color: #333;
}

.letter-date {
    text-align: right;
    color: #666;
    font-style: italic;
    margin-bottom: 1rem;
}

.letter-greeting {
    font-family: 'Dancing Script', cursive;
    font-size: 1.3rem;
    color: #e91e63;
    margin-bottom: 1rem;
}

.letter-content p {
    margin-bottom: 1rem;
}

.letter-signature {
    margin-top: 2rem;
    font-family: 'Dancing Script', cursive;
    font-size: 1.1rem;
    color: #e91e63;
    text-align: right;
}

.envelope-heart {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 2rem;
    z-index: 1;
    animation: heartPulse 2s infinite;
}

.click-hint {
    position: absolute;
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.9rem;
    color: #666;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-10px); }
    60% { transform: translateX(-50%) translateY(-5px); }
}

/* Countdown Section */
.countdown-container {
    text-align: center;
    max-width: 600px;
}

.countdown-display {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.countdown-item {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    min-width: 100px;
    border: 2px solid #e91e63;
}

.countdown-number {
    font-family: 'Dancing Script', cursive;
    font-size: 3rem;
    font-weight: 700;
    color: #e91e63;
    line-height: 1;
}

.countdown-label {
    font-size: 0.9rem;
    color: #666;
    margin-top: 0.5rem;
    font-weight: 500;
}

.countdown-separator {
    font-size: 2rem;
    color: #e91e63;
    font-weight: bold;
}

.countdown-message {
    font-size: 1.2rem;
    color: #666;
    font-style: italic;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 2000;
    justify-content: center;
    align-items: center;
}

.modal.show {
    display: flex;
    animation: modalFadeIn 0.5s ease;
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    max-width: 500px;
    width: 90%;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    transform: scale(0.8);
    animation: modalPopIn 0.5s ease forwards;
}

@keyframes modalPopIn {
    to {
        transform: scale(1);
    }
}

.modal-header h2 {
    font-family: 'Dancing Script', cursive;
    font-size: 2.5rem;
    color: #e91e63;
    margin-bottom: 1rem;
}

.surprise-message h3 {
    font-family: 'Great Vibes', cursive;
    font-size: 2rem;
    color: #e91e63;
    margin-bottom: 1rem;
}

.surprise-message p {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #333;
    margin-bottom: 1rem;
}

.birthday-cake {
    font-size: 4rem;
    margin: 2rem 0;
    animation: cakeWiggle 1s infinite;
}

@keyframes cakeWiggle {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-5deg); }
    75% { transform: rotate(5deg); }
}

.modal-close {
    background: linear-gradient(45deg, #ff6b9d, #e91e63);
    border: none;
    padding: 15px 30px;
    border-radius: 50px;
    color: white;
    font-size: 1.1rem;
    font-weight: 500;
    cursor: pointer;
    margin-top: 2rem;
    box-shadow: 0 8px 25px rgba(233, 30, 99, 0.3);
    transition: all 0.3s ease;
}

.modal-close:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(233, 30, 99, 0.4);
}

/* Confetti */
#confetti-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1500;
}

.confetti {
    position: absolute;
    width: 10px;
    height: 10px;
    background: #e91e63;
    animation: confettiFall 3s linear infinite;
}

@keyframes confettiFall {
    0% {
        transform: translateY(-100vh) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh) rotate(360deg);
        opacity: 0;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .section-title {
        font-size: 2.5rem;
    }
    
    .typewriter-text {
        font-size: 2.5rem;
    }
    
    .cursor {
        height: 2.5rem;
    }
    
    .envelope {
        width: 320px;
        height: 220px;
    }
    
    .countdown-display {
        gap: 0.5rem;
    }
    
    .countdown-item {
        padding: 1rem;
        min-width: 80px;
    }
    
    .countdown-number {
        font-size: 2rem;
    }
    
    .modal-content {
        padding: 2rem;
    }
}

@media (max-width: 480px) {
    .section {
        padding: 1rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .typewriter-text {
        font-size: 2rem;
    }
    
    .envelope {
        width: 280px;
        height: 200px;
    }
    
    .countdown-separator {
        display: none;
    }
}
