/* Reset dan Base Styles - Mobile First */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #ffeef8 0%, #ffe4e6 50%, #fdf2f8 100%);
    overflow-x: hidden;
    color: #4a4a4a;
    font-size: 14px;
    line-height: 1.6;
}

/* Music Control - Mobile First */
.music-control {
    position: fixed;
    top: 15px;
    right: 15px;
    z-index: 1000;
}

.music-btn {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.music-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.music-icon {
    font-size: 20px;
    animation: musicPulse 2s infinite;
}

@keyframes musicPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Section Styles - Mobile First */
.section {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 1rem;
    position: relative;
    opacity: 0;
    transform: translateY(50px);
    transition: all 1s ease;
}

.section.active {
    opacity: 1;
    transform: translateY(0);
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    font-family: 'Dancing Script', cursive;
    font-size: 2.2rem;
    color: #e91e63;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.section-subtitle {
    font-size: 1rem;
    color: #666;
    font-weight: 300;
    text-align: center;
    padding: 0 1rem;
}

/* Opening Section */
#opening {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: white;
    position: relative;
    overflow: hidden;
    justify-content: space-between;
    padding-bottom: 2rem;
}

#starsCanvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.opening-content {
    position: relative;
    z-index: 2;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex: 1;
    width: 100%;
}

.typewriter-container {
    position: relative;
    margin-bottom: 2rem;
}

.typewriter-text {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 600;
    color: #ffd700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    min-height: 1.2em;
    text-align: center;
    padding: 0 1rem;
}

.cursor {
    display: inline-block;
    width: 3px;
    height: 2.5rem;
    background: #ffd700;
    animation: blink 1s infinite;
    margin-left: 5px;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.subtitle {
    opacity: 0;
    animation: fadeInUp 1s ease forwards;
    animation-delay: 3s;
}

.fade-in-delayed {
    animation-delay: 4s;
}

.subtitle p {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    color: #e8e8e8;
}

.birth-date {
    font-family: 'Dancing Script', cursive;
    font-size: 1.8rem !important;
    color: #ffd700 !important;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Continue Button - Mobile First */
.continue-btn {
    background: linear-gradient(45deg, #ff6b9d, #e91e63);
    border: none;
    padding: 12px 24px;
    border-radius: 50px;
    color: white;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    margin-top: auto;
    margin-bottom: 1rem;
    box-shadow: 0 8px 25px rgba(233, 30, 99, 0.3);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    opacity: 0;
    animation: fadeInUp 1s ease forwards;
    animation-delay: 5s;
    min-width: 140px;
    align-self: center;
}

.continue-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(233, 30, 99, 0.4);
}

.heart-pulse {
    animation: heartPulse 1.5s infinite;
}

@keyframes heartPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

/* Gallery Section */
.gallery-container {
    max-width: 800px;
    width: 100%;
    position: relative;
}

.gallery-slider {
    position: relative;
    height: 500px;
    overflow: hidden;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.gallery-item {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.8s ease;
    background: white;
    display: flex;
    flex-direction: column;
}

.gallery-item.active {
    opacity: 1;
    transform: translateX(0);
}

.image-placeholder {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #ffeef8, #ffe4e6);
    color: #e91e63;
}

.placeholder-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.caption {
    padding: 2rem;
    text-align: center;
    background: white;
}

.caption h3 {
    font-family: 'Dancing Script', cursive;
    font-size: 2rem;
    color: #e91e63;
    margin-bottom: 1rem;
}

.caption p {
    font-size: 1.1rem;
    color: #666;
    line-height: 1.6;
}

/* Gallery Controls */
.gallery-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
    padding: 0 2rem;
}

.gallery-btn {
    background: rgba(233, 30, 99, 0.1);
    border: 2px solid #e91e63;
    color: #e91e63;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.gallery-btn:hover {
    background: #e91e63;
    color: white;
    transform: scale(1.1);
}

.gallery-dots {
    display: flex;
    gap: 10px;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(233, 30, 99, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active {
    background: #e91e63;
    transform: scale(1.3);
}

/* Letter Section - Mobile First */
.letter-container {
    perspective: 1000px;
    margin-bottom: 2rem;
    width: 100%;
    max-width: 400px;
}

.envelope {
    position: relative;
    width: 100%;
    height: 220px;
    cursor: pointer;
    transition: transform 0.3s ease;
    margin-bottom: 2rem;
}

.envelope:hover {
    transform: scale(1.02);
}

.envelope-body {
    width: 100%;
    height: 180px;
    background: linear-gradient(135deg, #fff8e1, #fffde7);
    border: 2px solid #e91e63;
    border-radius: 10px;
    position: relative;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.envelope-flap {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    height: 100px;
    background: linear-gradient(135deg, #ffcdd2, #f8bbd9);
    border: 2px solid #e91e63;
    border-radius: 10px 10px 0 0;
    transform-origin: bottom;
    transition: transform 0.8s ease;
    z-index: 3;
}

.envelope.opened .envelope-flap {
    transform: rotateX(-180deg);
}

.letter-paper {
    width: 100%;
    max-width: 500px;
    height: 400px;
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border: 2px solid #e91e63;
    transform: translateY(50px) scale(0.9);
    opacity: 0;
    transition: all 0.8s ease;
    margin-top: 1rem;
    display: none;
    position: relative;
    overflow-y: auto;
    overflow-x: hidden;
}

.envelope.opened + .letter-paper {
    transform: translateY(0) scale(1);
    opacity: 1;
    display: block;
}

/* Custom Scrollbar for Letter */
.letter-paper::-webkit-scrollbar {
    width: 8px;
}

.letter-paper::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.letter-paper::-webkit-scrollbar-thumb {
    background: #e91e63;
    border-radius: 4px;
}

.letter-paper::-webkit-scrollbar-thumb:hover {
    background: #c2185b;
}

.letter-content {
    font-size: 1rem;
    line-height: 1.8;
    color: #333;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    position: relative;
}

.letter-typewriter {
    font-family: 'Inter', sans-serif;
    font-size: 1rem;
    line-height: 1.8;
    color: #333;
    text-align: left;
    width: 100%;
    min-height: 100%;
}

.letter-typewriter p {
    margin-bottom: 1rem;
    text-align: justify;
}

.letter-typewriter p:last-child {
    text-align: right;
    font-style: italic;
    color: #e91e63;
    font-weight: 500;
    margin-top: 2rem;
}

.letter-cursor {
    display: inline-block;
    color: #e91e63;
    font-weight: bold;
    animation: letterBlink 1s infinite;
    font-size: 1.2rem;
}

@keyframes fadeInLine {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes letterBlink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.envelope-heart {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 2rem;
    z-index: 1;
    animation: heartPulse 2s infinite;
}

.click-hint {
    position: absolute;
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.9rem;
    color: #666;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-10px); }
    60% { transform: translateX(-50%) translateY(-5px); }
}

/* Countdown Section */
.countdown-container {
    text-align: center;
    max-width: 600px;
}

.countdown-display {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.countdown-item {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    min-width: 100px;
    border: 2px solid #e91e63;
}

.countdown-number {
    font-family: 'Dancing Script', cursive;
    font-size: 3rem;
    font-weight: 700;
    color: #e91e63;
    line-height: 1;
}

.countdown-label {
    font-size: 0.9rem;
    color: #666;
    margin-top: 0.5rem;
    font-weight: 500;
}

.countdown-separator {
    font-size: 2rem;
    color: #e91e63;
    font-weight: bold;
}

.countdown-message {
    font-size: 1.2rem;
    color: #666;
    font-style: italic;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 2000;
    justify-content: center;
    align-items: center;
}

.modal.show {
    display: flex;
    animation: modalFadeIn 0.5s ease;
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    max-width: 500px;
    width: 90%;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    transform: scale(0.8);
    animation: modalPopIn 0.5s ease forwards;
}

@keyframes modalPopIn {
    to {
        transform: scale(1);
    }
}

.modal-header h2 {
    font-family: 'Dancing Script', cursive;
    font-size: 2.5rem;
    color: #e91e63;
    margin-bottom: 1rem;
}

.surprise-message h3 {
    font-family: 'Great Vibes', cursive;
    font-size: 2rem;
    color: #e91e63;
    margin-bottom: 1rem;
}

.surprise-message p {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #333;
    margin-bottom: 1rem;
}

.birthday-cake {
    font-size: 4rem;
    margin: 2rem 0;
    animation: cakeWiggle 1s infinite;
}

@keyframes cakeWiggle {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-5deg); }
    75% { transform: rotate(5deg); }
}

.modal-close {
    background: linear-gradient(45deg, #ff6b9d, #e91e63);
    border: none;
    padding: 15px 30px;
    border-radius: 50px;
    color: white;
    font-size: 1.1rem;
    font-weight: 500;
    cursor: pointer;
    margin-top: 2rem;
    box-shadow: 0 8px 25px rgba(233, 30, 99, 0.3);
    transition: all 0.3s ease;
}

.modal-close:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(233, 30, 99, 0.4);
}

/* Confetti */
#confetti-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1500;
}

.confetti {
    position: absolute;
    width: 10px;
    height: 10px;
    background: #e91e63;
    animation: confettiFall 3s linear infinite;
}

@keyframes confettiFall {
    0% {
        transform: translateY(-100vh) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh) rotate(360deg);
        opacity: 0;
    }
}

/* Responsive Design - Mobile First Approach */

/* Tablet Styles */
@media (min-width: 768px) {
    body {
        font-size: 16px;
    }

    .section {
        padding: 2rem;
    }

    .section-title {
        font-size: 3rem;
    }

    .section-subtitle {
        font-size: 1.1rem;
    }

    .typewriter-text {
        font-size: 3.2rem;
    }

    .cursor {
        height: 3.2rem;
    }

    .continue-btn {
        padding: 14px 28px;
        font-size: 1.05rem;
        margin-top: 2.5rem;
    }

    .letter-container {
        max-width: 500px;
    }

    .envelope {
        height: 250px;
    }

    .envelope-body {
        height: 200px;
    }

    .envelope-flap {
        height: 110px;
    }

    .letter-paper {
        max-width: 600px;
        height: 500px;
        padding: 2.5rem;
    }

    .letter-typewriter {
        font-size: 1.1rem;
        line-height: 1.9;
    }

    .countdown-item {
        padding: 1.2rem;
        min-width: 90px;
    }

    .countdown-number {
        font-size: 2.2rem;
    }

    .music-btn {
        width: 50px;
        height: 50px;
    }

    .music-control {
        top: 20px;
        right: 20px;
    }
}

/* Desktop Styles */
@media (min-width: 1024px) {
    .section {
        padding: 3rem;
    }

    .section-title {
        font-size: 3.5rem;
    }

    .section-subtitle {
        font-size: 1.2rem;
    }

    .typewriter-text {
        font-size: 4rem;
    }

    .cursor {
        height: 4rem;
    }

    .continue-btn {
        padding: 15px 30px;
        font-size: 1.1rem;
        margin-top: 3rem;
    }

    .letter-container {
        max-width: 600px;
    }

    .envelope {
        height: 280px;
    }

    .envelope-body {
        height: 220px;
    }

    .envelope-flap {
        height: 120px;
    }

    .letter-paper {
        max-width: 700px;
        height: 600px;
        padding: 3rem;
    }

    .letter-typewriter {
        font-size: 1.2rem;
        line-height: 2;
    }

    .countdown-item {
        padding: 1.5rem;
        min-width: 100px;
    }

    .countdown-number {
        font-size: 2.5rem;
    }

    .modal-content {
        padding: 3rem;
        max-width: 600px;
    }
}

/* Large Desktop */
@media (min-width: 1440px) {
    .section-title {
        font-size: 4rem;
    }

    .typewriter-text {
        font-size: 4.5rem;
    }

    .cursor {
        height: 4.5rem;
    }

    .letter-container {
        max-width: 700px;
    }

    .letter-paper {
        max-width: 800px;
        height: 700px;
        padding: 4rem;
    }

    .letter-typewriter {
        font-size: 1.3rem;
        line-height: 2.2;
    }

    .countdown-number {
        font-size: 3rem;
    }
}
