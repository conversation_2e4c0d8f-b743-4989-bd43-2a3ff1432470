<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - Halaman Tidak <PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #06b6d4 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            overflow: hidden;
        }
        
        .container {
            text-align: center;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            max-width: 500px;
            width: 90%;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .error-code {
            font-size: 6rem;
            font-weight: bold;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            animation: bounce 2s infinite;
            background: linear-gradient(45deg, #fbbf24, #f59e0b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        
        .error-message {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            font-weight: 300;
        }
        
        .error-description {
            font-size: 1rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .redirect-info {
            background: rgba(255, 255, 255, 0.15);
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .redirect-text {
            font-size: 1.1rem;
            margin-bottom: 1rem;
            font-weight: 500;
        }
        
        .countdown-circle {
            position: relative;
            width: 80px;
            height: 80px;
            margin: 0 auto 1rem;
        }
        
        .countdown-number {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 2rem;
            font-weight: bold;
            color: #fbbf24;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }
        
        .progress-ring {
            transform: rotate(-90deg);
        }
        
        .progress-ring-circle {
            stroke: rgba(251, 191, 36, 0.3);
            stroke-width: 4;
            fill: transparent;
            r: 36;
            cx: 40;
            cy: 40;
        }
        
        .progress-ring-progress {
            stroke: #fbbf24;
            stroke-width: 4;
            fill: transparent;
            r: 36;
            cx: 40;
            cy: 40;
            stroke-dasharray: 226.19;
            stroke-dashoffset: 226.19;
            transition: stroke-dashoffset 1s linear;
            stroke-linecap: round;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 30px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            font-weight: 500;
            margin: 0 10px;
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn.primary {
            background: linear-gradient(45deg, #fbbf24, #f59e0b);
            border: 2px solid #fbbf24;
            color: #1f2937;
            font-weight: 600;
        }
        
        .btn.primary:hover {
            background: linear-gradient(45deg, #f59e0b, #d97706);
            transform: translateY(-2px) scale(1.05);
        }

        .button-container {
            margin-bottom: 1rem;
        }
        
        .icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .footer {
            margin-top: 2rem;
            font-size: 0.9rem;
            opacity: 0.7;
        }
        
        .loading-dots {
            display: inline-block;
        }
        
        .loading-dots span {
            animation: blink 1.4s infinite both;
        }
        
        .loading-dots span:nth-child(2) {
            animation-delay: 0.2s;
        }
        
        .loading-dots span:nth-child(3) {
            animation-delay: 0.4s;
        }
        
        @keyframes blink {
            0%, 80%, 100% { opacity: 0; }
            40% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">🔍</div>
        <div class="error-code">404</div>
        <div class="error-message">Halaman Tidak Ditemukan</div>
        <div class="error-description">
            Maaf, halaman yang Anda cari tidak dapat ditemukan atau telah dipindahkan.
        </div>
        
        <div class="redirect-info">
            <div class="redirect-text">
                Anda akan diarahkan ke halaman utama dalam<span class="loading-dots"><span>.</span><span>.</span><span>.</span></span>
            </div>
            
            <div class="countdown-circle">
                <svg class="progress-ring" width="80" height="80">
                    <circle class="progress-ring-circle"></circle>
                    <circle class="progress-ring-progress" id="progress"></circle>
                </svg>
                <div class="countdown-number" id="countdown">5</div>
            </div>
        </div>
        
        <div class="button-container">
            <a href="/index.php" class="btn primary">🏠 Ke Halaman Utama</a>
            <a href="javascript:history.back()" class="btn">↩️ Kembali</a>
        </div>
        
        <div class="footer">
            © 2025 &mdash; PT. Indo Web Solution
        </div>
    </div>
    
    <script>
        let countdown = 5;
        const countdownElement = document.getElementById('countdown');
        const progressElement = document.getElementById('progress');
        const circumference = 2 * Math.PI * 36; // 2πr where r=36
        
        // Set initial progress
        progressElement.style.strokeDasharray = circumference;
        progressElement.style.strokeDashoffset = circumference;
        
        function updateCountdown() {
            countdownElement.textContent = countdown;
            
            // Update progress ring
            const progress = ((5 - countdown) / 5) * circumference;
            progressElement.style.strokeDashoffset = circumference - progress;
            
            if (countdown <= 0) {
                // Redirect to main page
                window.location.href = '/index.php';
                return;
            }
            
            countdown--;
        }
        
        // Start countdown immediately
        updateCountdown();
        
        // Update every second
        const interval = setInterval(() => {
            updateCountdown();
            
            if (countdown < 0) {
                clearInterval(interval);
            }
        }, 1000);
        
        // Add click event to stop auto redirect if user interacts
        document.addEventListener('click', function(e) {
            if (e.target.tagName === 'A') {
                clearInterval(interval);
            }
        });
        
        // Add keyboard event to cancel redirect on any key press
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                clearInterval(interval);
                countdownElement.textContent = '⏸️';
                progressElement.style.stroke = '#ef4444';
            }
        });
    </script>
</body>
</html>
